import { api } from 'src/boot/axios'

export class UserService {
  static path = 'user'

  // istungk์ istung login
  static async login(name: string, password: string) {
    const res = await api.post('auth/login', { name, password })
    console.log('User logged in:', res.data)
    
    // Log user image path if available
    if (res.data && res.data.id) {
      try {
        // Get user image using the dedicated endpoint
        const imageUrl = await this.getUserImageById(res.data.id)
        console.log('User image URL from API:', imageUrl)
        
        // Add the image URL to the user data if found
        if (imageUrl) {
          res.data.processedImageUrl = imageUrl
        }
      } catch (error) {
        console.error('Error fetching user image:', error)
      }
    }
    
    return res.data
  }

  // istungk์ istung getAll
  static async getAll() {
    const res = await api.get(this.path)
    console.log('user :', res.data)
    return res.data
  }

  // istungk์ istung getUserInfo
  static async getUserInfo(userId: number) {
    try {
      const res = await api.get(`${this.path}/${userId}`)
      return res.data
    } catch (error) {
      console.error('Failed to fetch user data:', error)
      throw error
    }
  }

  // istungk์ istung getUserImage - returns full image URL
  static getUserImageUrl(imagePath: string) {
    // If no image path is provided, return null
    if (!imagePath) {
      console.log('No image path provided');
      return null;
    }

    // Log the incoming image path
    console.log('Original image path:', imagePath);

    // If the path is already a full URL, return it
    if (imagePath.startsWith('http')) {
      const fullUrl = imagePath;
      console.log('Using full URL:', fullUrl);
      return fullUrl;
    }

    // Special case for default image
    if (imagePath === 'noimage.png') {
      return null; // Return null to trigger the fallback icon
    }

    // Get base URL without trailing slash
    const baseURL = api.defaults.baseURL 
      ? (api.defaults.baseURL.endsWith('/') 
          ? api.defaults.baseURL.slice(0, -1) 
          : api.defaults.baseURL)
      : 'http://localhost:3000';

    // For user images, construct the specific path
    if (imagePath && !imagePath.startsWith('/')) {
      const userImageUrl = `${baseURL}/images/user/${imagePath}`;
      console.log('Constructed user image URL:', userImageUrl);
      return userImageUrl;
    }

    // Otherwise, construct the full URL using the API base URL
    const defaultUrl = `${baseURL}${imagePath.startsWith('/') ? '' : '/'}${imagePath}`;
    console.log('Constructed default URL:', defaultUrl);
    return defaultUrl;
  }

  // istungk์ istung filterUsers
  static async filterUsers(search: string = '', filter: string = '', role: string = '') {
    try {
      const res = await api.post(`${this.path}/filter`, { search, filter, role })
      console.log('Filtered users:', res.data)
      return res.data
    } catch (error) {
      console.error('Failed to filter users:', error)
      throw error
    }
  }

  // Get user image using the dedicated API endpoint
  static async getUserImageById(userId: number) {
    try {
      const res = await api.get(`${this.path}/image/${userId}`)
      console.log('User image data:', res.data)
      
      if (res.data && res.data.imagePath) {
        // Process the image path from the API response
        return this.getUserImageUrl(res.data.imagePath)
      }
      return null
    } catch (error) {
      console.error('Failed to fetch user image:', error)
      return null
    }
  }
}
