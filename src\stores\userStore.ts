import { defineStore } from 'pinia'
import { UserService } from 'src/services/userService'
import type { user } from 'src/types/user' // นำเข้า type user

export const useUserStore = defineStore('user', {
  state: () => ({
    users: [] as user[],
    currentUser: null as user | null,
    loading: false,
    error: null as string | null,
  }),

  actions: {
    async fetchUsers() {
      this.loading = true
      this.error = null
      try {
        const users = await UserService.getAll()
        this.users = users
      } catch (err) {
        this.error = 'Failed to fetch users.'
        console.error(err)
      } finally {
        this.loading = false
      }
    },

    async filterUsers(search: string = '', role: string = '') {
      this.loading = true
      this.error = null
      try {
        const users = await UserService.filterUsers(search, role)
        this.users = users
      } catch (err) {
        this.error = 'Failed to filter users.'
        console.error(err)
      } finally {
        this.loading = false
      }
    },

    async login(name: string, password: string) {
      this.loading = true
      this.error = null
      try {
        // ใช้ UserService ในการ login
        const userInfo = await UserService.login(name, password)

        // เก็บข้อมูลผู้ใช้ที่ login สำเร็จ
        this.currentUser = userInfo
      } catch (err) {
        this.error = 'Invalid credentials.'
        console.error(err)
      } finally {
        this.loading = false
      }
    },

    async fetchUserInfo(userId: number) {
      this.loading = true
      this.error = null
      try {
        const userInfo = await UserService.getUserInfo(userId)
        this.currentUser = userInfo
      } catch (err) {
        this.error = 'Failed to fetch user info.'
        console.error(err)
      } finally {
        this.loading = false
      }
    },

    setCurrentUser(userInfo: user) {
      this.currentUser = userInfo
    },

    clearCurrentUser() {
      this.currentUser = null
    },
  },

  getters: {
    allUsers: (state) => state.users,

    currentUserInfo: (state) => state.currentUser,
  },
})
