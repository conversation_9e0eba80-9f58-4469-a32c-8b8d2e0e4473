<template>
  <UserNavigation></UserNavigation>
  <div class="q-pa-md">
    <div class="container q-pa-md">
      <div class="text-h6 q-ml-md row justify-between items-center">
        <div>ข้อมูลการเข้างาน</div>
        <q-btn color="black" flat icon="arrow_back" @click="goBack" />
      </div>

      <!-- User info cards -->
      <div class="row q-col-gutter-md q-pa-md">
        <!-- User profile card -->
        <div class="col-12 col-md-3">
          <q-card flat class="user-info-card">
            <q-card-section class="row items-center no-wrap">
              <q-avatar size="60px" class="q-mr-md">
                <img :src="userImage" />
              </q-avatar>
              <div>
                <div class="text-weight-bold">{{ userData?.name || 'ไม่พบข้อมูล' }}</div>
                <div class="text-caption">{{ userData?.role || 'ประจำ' }}</div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Employee ID card -->
        <div class="col-12 col-md-3">
          <q-card flat class="user-info-card">
            <q-card-section class="flex justify-start items-center full-height">
              <div class="row items-center">
                <q-icon name="badge" size="60px" color="primary" class="q-mr-md" />
                <div>
                  <div class="text-caption">รหัสพนักงาน</div>
                  <div class="text-weight-bold">{{ userData?.id || '00000' }}</div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Phone number card -->
        <div class="col-12 col-md-3">
          <q-card flat class="user-info-card">
            <q-card-section class="flex justify-start items-center full-height">
              <div class="row items-center">
                <q-icon name="phone" size="60px" color="primary" class="q-mr-md" />
                <div>
                  <div class="text-caption">เบอร์ติดต่อ</div>
                  <div class="text-weight-bold">{{ userData?.phone || '-' }}</div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Branch card -->
        <div class="col-12 col-md-3">
          <q-card flat class="user-info-card">
            <q-card-section class="flex justify-start items-center full-height">
              <div class="row items-center">
                <q-icon name="store" size="60px" color="primary" class="q-mr-md" />
                <div>
                  <div class="text-caption">สาขา</div>
                  <div class="text-weight-bold">{{ userData?.branch || 'บางแสน' }}</div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Attendance records table -->
      <div class="q-pa-md">
        <q-table
          :rows="attendanceRecords"
          :columns="columns"
          row-key="date"
          :pagination="{ rowsPerPage: 10 }"
          flat
          bordered
          class="attendance-detail-table"
        >
          <template v-slot:body-cell-status="props">
            <q-td :props="props">
              <q-badge
                :class="{
                  'status-badge': true,
                  'holiday-badge': props.row.status === 'วันหยุด',
                  'leave-badge': props.row.status === 'ลาป่วย' || props.row.status === 'ลากิจ',
                  'work-badge': props.row.status === 'มาทำงาน',
                  'absent-badge': props.row.status === 'ขาดงาน',
                  'late-badge': props.row.status === 'มาสาย',
                }"
              >
                {{ props.row.status }}
              </q-badge>
            </q-td>
          </template>
        </q-table>

        <!-- Loading indicator -->
        <div v-if="loading" class="text-center q-pa-md">
          <q-spinner color="primary" size="2em" />
          <div class="q-mt-sm">โหลดข้อมูล...</div>
        </div>

        <!-- Error message -->
        <div v-if="error" class="text-center q-pa-md text-negative">
          {{ error }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import UserNavigation from 'src/components/userNavigation.vue'
import { UserService } from 'src/services/userService'
import { useAttendanceStore } from 'src/stores/attendance'
import { useUserStore } from 'src/stores/userStore'
import { date } from 'quasar'
import type { AttendanceRecord } from 'src/types/attendance'

// Define interfaces for the data structures
interface User {
  id: number
  name?: string
  role?: string
  phone?: string
  branch?: string
  [key: string]: unknown
}

interface FormattedAttendanceRecord {
  date: string
  clock_in: string
  clock_out: string
  hours: string
  status: string
  raw_date: string
}

const route = useRoute()
const router = useRouter()
const attendanceStore = useAttendanceStore()
const userStore = useUserStore()

const userId = ref(Number(route.params.id))

const userData = ref<User | null>(null)
const userImage = ref('https://cdn.quasar.dev/img/avatar.png')
const attendanceRecords = ref<FormattedAttendanceRecord[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// Table columns
const columns = [
  {
    name: 'date',
    label: 'วันที่',
    field: 'date',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'clock_in',
    label: 'เวลาเข้างาน',
    field: 'clock_in',
    align: 'center' as const,
  },
  {
    name: 'clock_out',
    label: 'เวลาออกงาน',
    field: 'clock_out',
    align: 'center' as const,
  },
  {
    name: 'hours',
    label: '<ชั่วโมงงาน',
    field: 'hours',
    align: 'center' as const,
  },
  {
    name: 'status',
    label: 'หมายเหตุ',
    field: 'status',
    align: 'center' as const,
  },
]

// Fetch user data
const fetchUserData = async () => {
  if (!userId.value) return

  try {
    // Get user details
    const user = userStore.users.find((u) => u.id === userId.value)
    if (user) {
      userData.value = {
        id: user.id,
        name: user.name,
        role: user.role,
        phone: user.tel,
        branch: user.branch?.name || 'บางแสน',
      }
    } else {
      // If not in store, fetch from API
      await userStore.fetchUsers()
      const fetchedUser = userStore.users.find((u) => u.id === userId.value)
      if (fetchedUser) {
        userData.value = {
          id: fetchedUser.id,
          name: fetchedUser.name,
          role: fetchedUser.role,
          phone: fetchedUser.tel,
          branch: fetchedUser.branch?.name || 'บางแสน',
        }
      }
    }

    // Get user image
    const imageUrl = await UserService.getUserImageById(userId.value)
    if (imageUrl) {
      userImage.value = imageUrl
    }
  } catch (error) {
    console.error('Error fetching user data:', error)
  }
}

// Fetch attendance records
const fetchAttendanceRecords = async () => {
  if (!userId.value) return

  loading.value = true
  error.value = null

  try {
    await attendanceStore.fetchUserAttendance(userId.value)
    const records = attendanceStore.attendanceRecords

    attendanceRecords.value = records.map((record: AttendanceRecord) => {
      const formattedDate = date.formatDate(new Date(record.date), 'YYYY-MM-DD')

      let status = '-'
      if (record.status === 'Present') {
        status = 'มาทำงาน'
      } else if (record.status === 'Late') {
        status = 'มาสาย'
      } else if (record.status === 'Absent') {
        status = 'ขาดงาน'
      } else if (record.status === 'Sick Leave') {
        status = 'ลาป่วย'
      } else if (record.status === 'Personal Leave') {
        status = 'ลากิจ'
      } else if (record.status === 'Leave') {
        status = 'ลา'
      }

      // ใช้เวลา work_duration ถ้ามี
      const hours = record.work_duration != null ? record.work_duration.toFixed(2) : '-'

      return {
        date: formattedDate,
        clock_in: record.clock_in || '-',
        clock_out: record.clock_out || '-',
        hours: hours,
        status: status,
        raw_date: record.date,
      }
    })

    // Sort by date
    attendanceRecords.value.sort((a, b) => {
      return new Date(b.raw_date).getTime() - new Date(a.raw_date).getTime()
    })
  } catch (err) {
    console.error('Error fetching attendance records:', err)
    error.value = 'ไม่สามารถโหลดข้อมูลการเข้างานได้'
  } finally {
    loading.value = false
  }
}

// Go back to attendance table
const goBack = () => {
  router.go(-1)
}

onMounted(async () => {
  await fetchUserData()
  await fetchAttendanceRecords()
})
</script>

<style scoped>
.container {
  background-color: white;
  border-radius: 10px;
}

.user-info-card {
  background-color: #e1edea;
  border-radius: 10px;
  height: 100%;
}

.attendance-detail-table {
  border-radius: 8px;
  overflow: hidden;
}

.attendance-detail-table :deep(th) {
  background-color: #e1edea !important;
  font-weight: bold;
}

.status-badge {
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  font-weight: bold;
  display: inline-block;
}

.holiday-badge {
  background-color: #b53638;
  color: white;
}

.work-badge {
  background-color: #1f6336;
  color: white;
}

.absent-badge {
  background-color: #439e62;
  color: white;
}

.leave-badge {
  background-color: #b53638;
  color: white;
}

.late-badge {
  background-color: #ed9b53;
  color: white;
}
</style>
