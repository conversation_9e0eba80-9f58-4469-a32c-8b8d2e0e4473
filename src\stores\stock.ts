import { defineStore, acceptHMRUpdate } from 'pinia'
import type { Product } from 'src/types/product'
import type { Stock } from 'src/types/stock'
import { useProductStore } from './product'
import { Dialog, type QTableColumn } from 'quasar'
import { StockService } from 'src/services/stock'
import { ref } from 'vue'

type TitleForm = 'New Subject' | 'Edit Subject'
export const defaultProduct: Product = {
  id: 0,
  product_code: '',
  product_name: '',
  generic_name: '',
  standard_cost: 0,
  selling_price: 0,
  storage_location: '',
  stock_min: 0,
  stock_max: 0,
  packing_size: '',
  reg_no: '',
  manufacturer: {
    id: 0,
    supplier_number: '',
    name: '',
    address: '',
    tel: '',
    tax_number: '',
    contact_name: '',
    fax: '',
    email: '',
    type: {
      id: 0,
      name: '',
    },
    isactive: false,
  },
  distributor: {
    id: 0,
    supplier_number: '',
    name: '',
    address: '',
    tel: '',
    tax_number: '',
    contact_name: '',
    fax: '',
    email: '',
    type: {
      id: 0,
      name: '',
    },
    isactive: false,
  },
  indications: '',
  warnings: '',
  purchase_notes: '',
  cost_notes: '',
  sales_alert_message: '',
  generic_group: '',
  product_group: {
    id: 0,
    name: '',
  },
  wholesale_control_group: '',
  special_report_group: {
    id: 0,
    name: '',
  },
  unit: '',
  barcode: '',
  isactive: false,
  wholesale: false,
  wholesale1: 0,
  wholesale2: 0,
  wholesale3: 0,
  wholesale4: 0,
  wholesale5: 0,
  retail: false,
  retail1: 0,
  retail2: 0,
  retail3: 0,
  retail4: 0,
  retail5: 0,
}
export const defaultStock: Stock = {
  id: 0,
  product: { ...defaultProduct }, // ใช้ Spread Operator เพื่อป้องกันการอ้างอิงค่าเดิม
  status: 'สินค้าคงอยู่',
  remaining: 0,
  branch: {
    id: 0,
    name: '',
    address: '',
  },
}
export const useStockStore = defineStore('stock', {
  state: () => ({
    form: {} as Stock,
    formOrderItems: {} as Stock,
    dialogState: false,
    stocks: [] as Stock[],
    products: [] as Product[],
    product: useProductStore(),
    titleForm: '' as TitleForm,
    id: 0,
    qDialog: Dialog,
    searchText: ref(''),
    selectedFilter: ref(''),
    selectedBranch: ref(''),
    stockColumns: <QTableColumn[]>[
      {
        name: 'id',
        label: 'ลำดับ',
        field: 'id',
        align: 'left',
        sortable: true,
      },
      {
        name: 'product_code',
        label: 'รหัสสินค้า',
        field: (row) => (row.product ? row.product.product_code : '-'),
        align: 'left',
        sortable: true,
      },
      {
        name: 'product_name',
        label: 'ชื่อสินค้า',
        field: (row) => (row.product ? row.product.product_name : '-'),
        align: 'left',
      },
      {
        name: 'unit',
        label: 'หน่วยฐาน',
        field: (row) => (row.product ? row.product.unit : '-'),
        align: 'left',
      },
      {
        name: 'standard_cost',
        label: 'ราคาขาย(บาท)',
        field: (row) => (row.product ? row.product.standard_cost : '-').toFixed(2),
        align: 'left',
      },
      {
        name: 'remaining',
        label: 'คงเหลือ',
        field: (row) => row.remaining,
        align: 'left',
      },
      {
        name: 'stock_min',
        label: 'จำนวนขั้นต่ำ',
        field: (row) => (row.product ? row.product.stock_min : '-'),
        align: 'left',
      },
      {
        name: 'stock_max',
        label: 'จำนวนขั้นสูง',
        field: (row) => (row.product ? row.product.stock_max : '-'),
        align: 'left',
      },
      {
        name: 'status',
        label: 'สถานะ',
        field: (row) => row.status,
        align: 'left',
      },
      {
        name: 'actions',
        label: '',
        align: 'center' as const,
        field: 'actions',
        sortable: false,
      },
    ],
    stocksDialog: [] as Stock[],
    searchTextDialog: ref(''),
    selectedFilterDialog: ref(''),
    selectedBranchDialog: ref(''),
  }),

  getters: {
    getProductById:
      (state) =>
        (id: number): Product | undefined =>
          state.products.find((p) => p.id === id),
    getStocks: (s) => s.stocks,
    getStocksDialog: (s) => s.stocksDialog,
    getDialogTitle: (s) => s.titleForm,
  },

  actions: {
    async fetchAllStock() {
      try {
        const data = await StockService.getAll()
        if (data) {
          this.stocks = data
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    },
    async fetchAllStockByFilter() {
      try {
        const data = await StockService.getAllByFilter(
          this.searchText,
          this.selectedFilter,
          this.selectedBranch,
        )
        if (data) {
          this.stocks = data
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    },

    async fetchAllStockByFilterDialog() {
      console.log('fetchAllStockByFilterDialog')
      try {
        console.log('Fetching stocks for branch:', this.selectedBranchDialog)
        const data = await StockService.getAllByFilterDialog(
          this.searchTextDialog,
          this.selectedFilterDialog,
          this.selectedBranchDialog, // ใช้ selectedBranchDialog
        )
        if (data) {
          this.stocksDialog = data
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    },

    addProduct(product: Product) {
      product.id = this.id + 1
      this.products.push(product)
      this.id += 1
      console.log(this.products)
    },

    async updateOne() {
      try {
        const data = await StockService.updateOne(this.form.id, this.form)
        if (data) {
          await this.fetchAllStock()
        }
      } catch (error) {
        console.error('Error updating stock:', error)
      }
    },
    async handleSave() {
      await this.updateOne()
      this.resetForm()
      console.log(this.form)
      await this.fetchAllStock()
    },
    resetForm() {
      this.form = JSON.parse(JSON.stringify(defaultStock)) // ป้องกัน Object อ้างอิงค่าเดิม
    },
    removeProduct(productId: number) {
      this.products = this.products.filter((p) => p.id !== productId)
    },
    resetFormOrderItems() {
      this.formOrderItems = JSON.parse(JSON.stringify(defaultStock)) // ป้องกัน Object อ้างอิงค่าเดิม
    },
  },
})

// Hot Module Replacement (HMR)
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useProductStore, import.meta.hot))
}
